version: '3.8'

services:
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "5008:5008"
    volumes:
      - ./backend/config.toml:/app/config.toml
    restart: unless-stopped

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      args:
        - REACT_APP_API_URL=http://localhost:5008/api
        - REACT_APP_TITLE=AI 工作台
    ports:
      - "3008:80"
    depends_on:
      - backend
    restart: unless-stopped