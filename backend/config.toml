# API配置
[api]
PORT = 5008
HOST = "0.0.0.0"
DEBUG = true

# CORS配置
[cors]
CORS_ORIGINS = "http://localhost:3008,https://your-frontend-domain.com"

# AI 模型配置
[ai]
LLM_API_KEY = "sk-pqkelnO3f7F5eW8_yj5OpGk_b-Wn73AxYRqM00uqMAZd97U2k0YDWO-r2OM"
LLM_MODEL_NAME = "deepseek-v3"
LLM_BASE_URL = "https://oneapi.01sworld.top:8443/v1"

# Azure TTS 配置
[azure_tts]
AZURE_TTS_KEY = "E9wHI5q3mSI9RRsCo9OmAEQmKFoEMVCdFYaXdBTqqBUpiaKzZlirJQQJ99ALACHYHv6XJ3w3AAAAACOGhYsJ"
AZURE_TTS_REGION = "eastus2"

# Postgres 数据库配置
[postgres]
host = "*************"
port = 5432
user = "user"
password = "pass123"
database = "aipodcast"

# MinIO S3 配置
[minio]
endpoint = "*************:9000"
access_key = "mUpZulr9zy9oKiEK4Ph7"
secret_key = "05oecQnnTIMNkd7M6tMDPpuU3OLWct7h4PAUruby"
bucket = "aipodcast"
region = ""  # 可选

# Workflow 配置
[workflow]
workflow_url = "http://*************:5678/webhook"

# VOICE_MAP 配置
# voiceMap 结构已统一为对象数组，每项包含 key（语音标识）和 desc（描述）。
[voice_map.zh]
0 = [
        {key="zh-CN-Xiaoxiao2:DragonHDFlashLatestNeural", desc="Xiaoxiao2HDFlash"}, 
        {key="zh-CN-Xiaochen:DragonHDFlashLatestNeural", desc="XiaochenHDFlash"}, 
        {key="zh-CN-Xiaoxiao:DragonHDFlashLatestNeural", desc="XiaoxiaoHDFlash"}, 
        {key="zh-CN-Xiaochen:DragonHDLatestNeural", desc="XiaochenHD"},
        {key="zh-CN-XiaoxiaoNeural", desc="Xiaoxiao"},
        {key="zh-CN-XiaochenNeural", desc="Xiaochen"}
    ]
1 = [
        {key="zh-CN-Yunyi:DragonHDFlashLatestNeural", desc="YunyiHDFlash"}, 
        {key="zh-CN-Yunxiao:DragonHDFlashLatestNeural", desc="YunxiaoHDFlash"}, 
        {key="zh-CN-Yunfan:DragonHDLatestNeural", desc="YunfanHD"}, 
        {key="zh-CN-YunyangNeural", desc="Yunyang"}
    ]

[voice_map.en]
0 = [{key="en-US-JennyNeural", desc="Jenny"}, {key="en-US-AriaNeural", desc="Aria"}]
1 = [{key="en-US-GuyNeural", desc="Guy"}, {key="en-US-DavisNeural", desc="Davis"}]