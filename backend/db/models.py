from datetime import datetime

from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey
from sqlalchemy.orm import declarative_base, relationship

Base = declarative_base()

class PodcastScript(Base):
    __tablename__ = "podcast_script"

    id = Column(Integer, primary_key=True, autoincrement=True)
    original_text = Column(Text, nullable=False)
    summary_text = Column(Text, nullable=True)
    script_text = Column(Text, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)

    def __repr__(self):
        return f"<PodcastScript(id={self.id})>"

class PodcastAudio(Base):
    __tablename__ = "podcast_audio"

    id = Column(Integer, primary_key=True, autoincrement=True)
    script_text = Column(Text, nullable=False)
    voice_model_name = Column(String(128), nullable=False)
    audio_url = Column(String(512), nullable=False)
    edit_audio_url = Column(String(512), nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)

    def __repr__(self):
        return (
            f"<PodcastAudio(id={self.id}, "
            f"script_text={self.script_text!r}, "
            f"voice_model_name={self.voice_model_name!r}, "
            f"audio_url={self.audio_url!r}, "
            f"edit_audio_url={self.edit_audio_url!r}, "
            f"created_at={self.created_at})>"
        )
class TTSRecord(Base):
    __tablename__ = "tts_record"

    id = Column(Integer, primary_key=True, autoincrement=True)
    text = Column(Text, nullable=False)
    voice = Column(String(128), nullable=False)
    audio_url = Column(String(512), nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)

    def __repr__(self):
        return (
            f"<TTSRecord(id={self.id}, text={self.text!r}, voice={self.voice!r}, "
            f"audio_url={self.audio_url!r}, created_at={self.created_at})>"
        )

# === 以下为“思维模型”的定义 ===
class AIVideoScript(Base):
    """
    主表：存储思维模型脚本及元信息

    type 字段：用于标记脚本类型，可为空。可用于区分不同用途的脚本（如“标准”、“测试”、“草稿”等）。
    """
    __tablename__ = "aivideo_script"

    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键")
    input = Column(String(128), nullable=False, comment="主题")
    content = Column(Text, nullable=True, comment="正文内容")
    name = Column(String(256), nullable=False, comment="标题")
    draft_url = Column(String(512), nullable=True, comment="草稿URL")
    type = Column(String(64), nullable=True, comment="脚本类型，可用于区分不同用途的脚本")
    debug_url = Column(String(512), nullable=True, comment="调试URL")
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, comment="创建时间")

    texts = relationship("AIVideoScriptText", back_populates="script", cascade="all, delete-orphan")
    image_prompts = relationship("AIVideoScriptImagePrompt", back_populates="script", cascade="all, delete-orphan")
    images = relationship("AIVideoScriptImage", back_populates="script", cascade="all, delete-orphan")
    materials = relationship("AIVideoMaterial", back_populates="script", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<AIVideoScript(id={self.id}, name={self.name!r})>"

class AIVideoScriptImage(Base):
    """
    图片列表：存储脚本相关图片URL
    """
    __tablename__ = "aivideo_script_image"

    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键")
    script_id = Column(Integer, ForeignKey("aivideo_script.id"), nullable=False, comment="关联脚本ID")
    url = Column(String(512), nullable=False, comment="图片URL")

    script = relationship("AIVideoScript", back_populates="images")

    def __repr__(self):
        return f"<AIVideoScriptImage(id={self.id}, script_id={self.script_id}, url={self.url!r})>"

class AIVideoScriptText(Base):
    """
    文本片段列表
    """
    __tablename__ = "aivideo_script_text"

    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键")
    script_id = Column(Integer, ForeignKey("aivideo_script.id"), nullable=False, comment="关联脚本ID")
    text = Column(Text, nullable=False, comment="文本内容")

    script = relationship("AIVideoScript", back_populates="texts")

    def __repr__(self):
        return f"<AIVideoScriptText(id={self.id}, script_id={self.script_id})>"

class AIVideoScriptImagePrompt(Base):
    """
    图片提示词列表
    """
    __tablename__ = "aivideo_script_image_prompt"

    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键")
    script_id = Column(Integer, ForeignKey("aivideo_script.id"), nullable=False, comment="关联脚本ID")
    prompt = Column(Text, nullable=False, comment="图片提示词")

    script = relationship("AIVideoScript", back_populates="image_prompts")

    def __repr__(self):
        return f"<AIVideoScriptImagePrompt(id={self.id}, script_id={self.script_id})>"

class AIVideoMaterial(Base):
    """
    素材组，包含音频、时长、文本等
    """
    __tablename__ = "aivideo_material"

    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键")
    script_id = Column(Integer, ForeignKey("aivideo_script.id"), nullable=False, comment="关联脚本ID")

    script = relationship("AIVideoScript", back_populates="materials")
    items = relationship("AIVideoMaterialItem", back_populates="material", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<AIVideoMaterial(id={self.id}, script_id={self.script_id})>"

class AIVideoMaterialItem(Base):
    """
    素材条目，合并音频、文本、时长等信息
    """
    __tablename__ = "aivideo_material_item"

    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键")
    material_id = Column(Integer, ForeignKey("aivideo_material.id"), nullable=False, comment="关联素材ID")
    text = Column(Text, nullable=True, comment="文本内容")
    audio_url = Column(String(512), nullable=True, comment="音频URL")

    material = relationship("AIVideoMaterial", back_populates="items")

    def __repr__(self):
        return f"<AIVideoMaterialItem(id={self.id}, material_id={self.material_id})>"