import requests
from backend.services.minio_service import batch_download_and_upload_to_minio
from backend.config import get_workflow_url
import copy
import json

class AIVideoServiceError(Exception):
    """自定义异常：AIVideoService 相关错误"""
    pass
from sqlalchemy import desc

# type（视频风格）与外部接口的映射关系
# 每种风格（type）下有 video/image/audio 三类生成接口
type_to_external_api = {
    "sketch": {
        "video": "{base_url}/aiworkflow/sketch/generate/video",
        "video_part1": "{base_url}/aiworkflow/sketch/generate/video_part1",
        "video_part2": "{base_url}/aiworkflow/sketch/generate/video_part2",
        "image": "{base_url}/aiworkflow/sketch/generate/single_image",
        "audio": "{base_url}/aiworkflow/sketch/generate/single_audio",
    },
    "cartoon": {
        "video": "{base_url}/aiworkflow/cartoon/generate/video",
        "video_part1": "{base_url}/aiworkflow/cartoon/generate/video_part1",
        "video_part2": "{base_url}/aiworkflow/cartoon/generate/video_part2",
        "image": "{base_url}/aiworkflow/cartoon/generate/single_image",
        "audio": "{base_url}/aiworkflow/cartoon/generate/single_audio",
    },
    "realistic": {
        "video": "{base_url}/aiworkflow/realistic/generate/video",
        "video_part1": "{base_url}/aiworkflow/realistic/generate/video_part1",
        "video_part2": "{base_url}/aiworkflow/realistic/generate/video_part2",
        "image": "{base_url}/aiworkflow/realistic/generate/single_image",
        "audio": "{base_url}/aiworkflow/realistic/generate/single_audio",
    },
}
from typing import Optional

def generate_aivideo(input_value: str, type: Optional[str] = "sketch") -> dict:
    """
    根据视频风格（type）调用对应外部 aivideo 接口，参数通过 body 传递，返回原始数据，异常抛出。
    :param input_value: 前端传入的 input 字符串
    :param type: 可选，视频风格
    :return: 外部接口返回的原始数据（dict），增加 type 字段
    :raises: AIVideoServiceError
    """
    base_url = get_workflow_url()
    if not type or type not in type_to_external_api or "video" not in type_to_external_api[type]:
        raise AIVideoServiceError(f"未配置该视频风格的 video 外部接口: {type}")
    url = type_to_external_api[type]["video"].format(base_url=base_url)
    payload = {"input": input_value}
    try:
        resp = requests.post(url, json=payload, timeout=900)
        resp.raise_for_status()
        result = resp.json()
        # 透传 type 字段
        if isinstance(result, dict):
            result["type"] = type
        elif isinstance(result, list) and len(result) > 0 and isinstance(result[0], dict):
            result[0]["type"] = type
        return result
    except Exception as e:
        raise AIVideoServiceError(f"外部 aivideo 接口请求失败: {str(e)}")

def upload_aivideo_assets_to_minio(data: dict) -> dict:
    """
    批量处理 imageList/audioList，上传到 minio，替换为 minio 地址，返回新数据。
    :param data: 包含 imageList/audioList 的原始数据
    :return: 替换为 minio 地址后的新数据
    :raises: AIVideoServiceError
    """
    try:
        new_data = copy.deepcopy(data)
        # 处理 imageList
        if "imageList" in new_data and isinstance(new_data["imageList"], list):
            minio_images = batch_download_and_upload_to_minio(new_data["imageList"])
            new_data["imageList"] = minio_images
        # 处理 audioList
        if "audioList" in new_data and isinstance(new_data["audioList"], list):
            minio_audios = batch_download_and_upload_to_minio(new_data["audioList"])
            new_data["audioList"] = minio_audios

        # 处理 materialList 内 audioList
        if "materialList" in new_data and isinstance(new_data["materialList"], list):
            for item in new_data["materialList"]:
                audio_list = item.get("audioList", [])
                # 收集所有 audio["data"]["link"]
                audio_links = []
                audio_indices = []
                for idx, audio in enumerate(audio_list):
                    if isinstance(audio, dict) and isinstance(audio.get("data", {}), dict) and "link" in audio["data"]:
                        audio_links.append(audio["data"]["link"])
                        audio_indices.append(idx)
                # 批量上传
                if audio_links:
                    minio_links = batch_download_and_upload_to_minio(audio_links)
                    # 写回
                    for i, idx in enumerate(audio_indices):
                        if i < len(minio_links):
                            audio = audio_list[idx]
                            if isinstance(audio, dict) and isinstance(audio.get("data", {}), dict):
                                audio["data"]["link"] = minio_links[i]
        return new_data
    except Exception as e:
        raise AIVideoServiceError(f"minio 批量上传失败: {str(e)}")

from backend.db.db import get_engine_and_sessionmaker
from backend.db.models import (
    AIVideoScript, AIVideoScriptImage, AIVideoMaterial, AIVideoScriptText,
    AIVideoScriptImagePrompt, AIVideoMaterialItem
)

def save_or_update_aivideo_script_assets(session, script, data: dict, *, clear_old: bool = False):
    """
    内部方法：保存/更新AIVideoScript及其多模态内容，兼容一步法与两步法。
    :param session: SQLAlchemy session
    :param script: 已有或新建的AIVideoScript实例
    :param data: dict，包含 imageList、textList、imagePromptList、materialList 等
    :param clear_old: 是否清理旧多模态内容（两步法为True，一步法为False）
    """
    if clear_old:
        session.query(AIVideoScriptImage).filter_by(script_id=script.id).delete()
        session.query(AIVideoScriptImagePrompt).filter_by(script_id=script.id).delete()
        session.query(AIVideoMaterial).filter_by(script_id=script.id).delete()
        session.query(AIVideoScriptText).filter_by(script_id=script.id).delete()
    # 图片 ScriptImage
    image_list = data.get("imageList", [])
    for url in image_list:
        img = AIVideoScriptImage(script_id=script.id, url=url)
        session.add(img)
    # 文本片段 AIVideoScriptText
    text_list = data.get("textList", [])
    if isinstance(text_list, str):
        try:
            text_list = json.loads(text_list)
        except Exception:
            text_list = [text_list]
    if not isinstance(text_list, list):
        text_list = [str(text_list)]
    for text in text_list:
        text_obj = AIVideoScriptText(script_id=script.id, text=text)
        session.add(text_obj)
    # 图片提示词 AIVideoScriptImagePrompt
    image_prompt_list = data.get("imagePromptList", [])
    for prompt in image_prompt_list:
        prompt_obj = AIVideoScriptImagePrompt(script_id=script.id, prompt=prompt)
        session.add(prompt_obj)
    # 素材 AIVideoMaterial + AIVideoMaterialItem
    material = AIVideoMaterial(script_id=script.id)
    session.add(material)
    session.flush()  # 获取 material.id
    material_list = data.get("materialList", [])
    for idx, item in enumerate(material_list):
        audio_list = item.get("audioList", [])
        text_list = item.get("textList", [])
        if isinstance(text_list, str):
            try:
                text_list = json.loads(text_list)
            except Exception:
                text_list = []
        elif not isinstance(text_list, list):
            text_list = []
        max_len = max(len(audio_list), len(text_list), 1)
        for i in range(max_len):
            audio = audio_list[i] if i < len(audio_list) else None
            text = text_list[i] if i < len(text_list) else None
            if isinstance(audio, dict):
                audio_url = (
                    audio.get("data", {}).get("link")
                    if isinstance(audio.get("data", {}), dict)
                    else None
                )
            else:
                audio_url = audio if isinstance(audio, str) else None
            item_obj = AIVideoMaterialItem(
                material_id=material.id,
                audio_url=audio_url,
                text=text
            )
            session.add(item_obj)

def save_aivideo_to_db(data: dict) -> int:
    """
    将处理后的数据存入数据库，结构与 models 一致，异常抛出。
    :param data: 处理后的 aivideo 数据
    :return: 新插入的 Script 主表 id
    :raises: AIVideoServiceError
    """
    _, SessionLocal = get_engine_and_sessionmaker()
    session = SessionLocal()
    try:
        # 主表 AIVideoScript
        script = AIVideoScript(
            input=data.get("input", ""),
            content=data.get("content", ""),
            name=data.get("name", ""),
            draft_url=data.get("draftURL", None),
            debug_url=data.get("debug_url", None),
            type=data.get("type", None)
        )
        session.add(script)
        session.flush()  # 获取 script.id

        # 公共多模态内容写入
        save_or_update_aivideo_script_assets(session, script, data, clear_old=False)

        session.commit()
        return script.id
    except Exception as e:
        session.rollback()
        raise AIVideoServiceError(f"保存 aivideo 到数据库失败: {str(e)}")
    finally:
        session.close()

def get_aivideo_list(page: int, page_size: int):
    """
    分页获取所有AI视频基本信息
    :param page: 页码（从1开始）
    :param page_size: 每页数量
    :return: {total, items: [{id, input, name, created_at}]}
    :raises: AIVideoServiceError
    """
    _, SessionLocal = get_engine_and_sessionmaker()
    session = SessionLocal()
    try:
        query = session.query(AIVideoScript)
        total = query.count()
        items = (
            query.order_by(desc(AIVideoScript.id))
            .offset((page - 1) * page_size)
            .limit(page_size)
            .all()
        )
        result = []
        for s in items:
            result.append({
                "id": s.id,
                "input": s.input,
                "name": s.name,
                "created_at": s.created_at
            })
        return {"total": total, "items": result}
    except Exception as e:
        raise AIVideoServiceError(f"获取AI视频列表失败: {str(e)}")
    finally:
        session.close()

def get_aivideo_detail(script_id: int):
    """
    获取指定AI视频的全部详情
    :param script_id: AIVideoScript主键
    :return: dict，包含 aivideo_script 及其 images、texts、image_prompts、materialList
        images: List[{"id": int, "url": str}]
        materialList: List[{"id": int, "text": str, "audio_url": str}]
    :raises: AIVideoServiceError
    """
    _, SessionLocal = get_engine_and_sessionmaker()
    session = SessionLocal()
    try:
        script = session.query(AIVideoScript).filter_by(id=script_id).first()
        if not script:
            raise AIVideoServiceError("未找到指定ID的AI视频")
        # 主体
        detail = {
            "id": script.id,
            "input": script.input,
            "content": script.content,
            "name": script.name,
            "draft_url": script.draft_url,
            "debug_url": script.debug_url,
            "type": script.type,
            "images": [{"id": img.id, "url": img.url} for img in script.images],
            "texts": [
                x["text"] for x in sorted(
                    [{"id": t.id, "text": t.text} for t in script.texts],
                    key=lambda x: x["id"]
                )
            ],
            "image_prompts": [
                x["prompt"] for x in sorted(
                    [{"id": p.id, "prompt": p.prompt} for p in script.image_prompts],
                    key=lambda x: x["id"]
                )
            ],
            "materialList": [],
            "created_at": script.created_at
        }
        # 聚合 materialList
        for material in script.materials:
            for item in material.items:
                detail["materialList"].append({
                    "id": item.id,
                    "text": item.text,
                    "audio_url": item.audio_url
                })
        # 对 images 按 id 升序排序（如存在且为数组且含 id 字段）
        if isinstance(detail["images"], list) and detail["images"] and "id" in detail["images"][0]:
            detail["images"].sort(key=lambda x: x["id"])
        # 对 materialList 按 id 升序排序（如存在且为数组且含 id 字段）
        if isinstance(detail["materialList"], list) and detail["materialList"] and "id" in detail["materialList"][0]:
            detail["materialList"].sort(key=lambda x: x["id"])
        return detail
    except Exception as e:
        raise AIVideoServiceError(f"获取AI视频详情失败: {str(e)}")
    finally:
        session.close()
        session.close()

def assemble_aivideo_response(data: dict) -> dict:
    """
    组装返回前端的数据结构，imageList/audioList为minio地址。
    :param data: 处理后的 aivideo 数据
    :return: 前端需要的数据结构
    :raises: AIVideoServiceError
    """
    try:
        resp = {
            "input": data.get("input", ""),
            "content": data.get("content", ""),
            "name": data.get("name", ""),
            "draftURL": data.get("draftURL", None),
            "debug_url": data.get("debug_url", None),
            "textList": data.get("textList", []),
            "imageList": data.get("imageList", []),
            "imagePromptList": data.get("imagePromptList", []),
        }
        material_list = data.get("materialList", [])
        resp["materialList"] = []
        for item in material_list:
            audio_list = item.get("audioList", [])
            text_list = item.get("textList", [])
            max_len = max(len(audio_list), len(text_list), 1)
            for i in range(max_len):
                audio = audio_list[i] if i < len(audio_list) else None
                text = text_list[i] if i < len(text_list) else None

                if isinstance(audio, dict):
                    audio_url = (
                        audio.get("data", {}).get("link")
                        if isinstance(audio.get("data", {}), dict)
                        else None
                    )
                else:
                    audio_url = audio if isinstance(audio, str) else None

                resp["materialList"].append({
                    "audio_url": audio_url,
                    "text": text
                })
        return resp
    except Exception as e:
        raise AIVideoServiceError(f"组装 aivideo 返回数据失败: {str(e)}")

def generate_aivideo_part1(input_value: str, type: Optional[str] = "sketch") -> dict:
    """
    AI视频“两步法”第一步：根据视频风格调用外部接口生成脚本，参数通过 body 传递，并保存input、content、textList等信息到数据库。
    仅保存脚本相关内容，不生成draft_url、debug_url等最终视频信息。
    :param input_value: 前端传入的 input 字符串
    :param type: 可选，视频风格
    :return: {id, input, content, textList, type}
    :raises: AIVideoServiceError
    """
    # 1. 调用外部接口
    base_url = get_workflow_url()
    if not type or type not in type_to_external_api or "video_part1" not in type_to_external_api[type]:
        raise AIVideoServiceError(f"未配置该视频风格的 video_part1 外部接口: {type}")
    url = type_to_external_api[type]["video_part1"].format(base_url=base_url)
    payload = {"input": input_value}
    try:
        resp = requests.post(url, json=payload, timeout=300)
        resp.raise_for_status()
        result = resp.json()
    except Exception as e:
        raise AIVideoServiceError(f"外部脚本生成接口请求失败: {str(e)}")

    # 2. 解析返回
    script_input = result[0].get("input", input_value)
    script_content = result[0].get("content", "")
    text_list = result[0].get("textList", [])
    # 兼容textList为字符串或数组
    if isinstance(text_list, str):
        try:
            text_list = json.loads(text_list)
        except Exception:
            text_list = [text_list]
    if not isinstance(text_list, list):
        text_list = [str(text_list)]

    # 3. 保存到数据库
    _, SessionLocal = get_engine_and_sessionmaker()
    session = SessionLocal()
    try:
        script = AIVideoScript(
            input=script_input,
            content=script_content,
            name=script_input if script_content == "" else script_content[:32],  # 标题可自定义
            draft_url=None,
            debug_url=None,
            type=type if type is not None else result[0].get("type", None)
        )
        session.add(script)
        session.flush()  # 获取 script.id

        for text in text_list:
            text_obj = AIVideoScriptText(script_id=script.id, text=text)
            session.add(text_obj)

        session.commit()
        resp_dict = {
            "id": script.id,
            "input": script_input,
            "content": script_content,
            "textList": text_list
        }
        if type is not None:
            resp_dict["type"] = type
        elif "type" in result[0]:
            resp_dict["type"] = result[0]["type"]
        return resp_dict
    except Exception as e:
        session.rollback()
        raise AIVideoServiceError(f"保存AI视频脚本到数据库失败: {str(e)}")
    finally:
        session.close()

def generate_aivideo_part2(script_id: int, content=None) -> dict:
    """
    AI视频“两步法”第二步：根据已保存的AIVideoScript，按视频风格调用外部接口生成最终视频，参数通过 body 传递，写入draft_url、debug_url等及多模态内容。
    :param script_id: AIVideoScript主键
    :param content: 可选，前端传入的内容，优先使用
    :return: 结构与generate_aivideo一致
    :raises: AIVideoServiceError
    """
    # 1. 查找脚本
    _, SessionLocal = get_engine_and_sessionmaker()
    session = SessionLocal()
    try:
        script = session.query(AIVideoScript).filter_by(id=script_id).first()
        if not script:
            raise AIVideoServiceError("未找到指定ID的AI视频脚本")
        # 2. 调用外部接口
        base_url = get_workflow_url()
        type = script.type if script.type else "sketch"
        if type not in type_to_external_api or "video_part2" not in type_to_external_api[type]:
            raise AIVideoServiceError(f"未配置该视频风格的 video_part2 外部接口: {type}")
        url = type_to_external_api[type]["video_part2"].format(base_url=base_url)
        content_val = content if content is not None else script.content
        payload = {
            "input": script.input,
            "content": content_val,
            "name": script.name,
            "id": script.id
        }
        try:
            resp = requests.post(url, json=payload, timeout=900)
            resp.raise_for_status()
            result = resp.json()
        except Exception as e:
            raise AIVideoServiceError(f"外部视频生成接口请求失败: {str(e)}")
        
        # 3. 处理多模态内容（上传minio等）
        data = upload_aivideo_assets_to_minio(result[0])
        # 4. 更新AIVideoScript主表
        script.draft_url = data.get("draftURL", None)
        script.debug_url = data.get("debug_url", None)
        script.content = data.get("content", content_val)
        script.name = data.get("name", script.name)
        # 5. 公共多模态内容清理与写入
        save_or_update_aivideo_script_assets(session, script, data, clear_old=True)
        session.commit()
        # 6. 返回结构与“一步法”一致
        return assemble_aivideo_response(data)
    except Exception as e:
        session.rollback()
        raise AIVideoServiceError(f"AI视频第二步入库失败: {str(e)}")
    finally:
        session.close()

def replace_asset_service(asset_id, asset_type, content):
    """
    替换素材核心服务方法，支持音频/图片，封装 webhook、minio、数据库更新。
    根据视频风格（type）选择外部接口，参数通过 body 传递。

    图片 prompt 替换采用 index 匹配原则：
    - 当 asset_type 为 "image" 时，先获取当前图片（AIVideoScriptImage）的 script_id。
    - 查询该 script_id 下所有图片，按 id 升序排序，找到当前图片在其中的 index。
    - 查询该 script_id 下所有 AIVideoScriptImagePrompt，按 id 升序排序，找到同 index 的 prompt 对象。
    - 用 content 替换该 prompt 对象的 prompt 字段。
    - 若未找到对应 prompt 对象，则不做 prompt 替换，且不报错。

    :param asset_id: 目标素材主键
    :param asset_type: 'audio' 或 'image'
    :param content:
        - 若 asset_type == "image"，content 作为新图片的 prompt
        - 若 asset_type == "audio"，content 作为新音频的 text
    :return: (url, code, msg)
    code: 0=成功，3=asset_id 为空，4=未找到对象，5=webhook 失败，6=minio 失败，7=数据库失败，8=参数错误
    """
    # 1. 参数检查
    if not asset_id:
        return None, 3, "asset_id 不能为空"
    if asset_type not in ("audio", "image"):
        return None, 8, "type 仅支持 audio 或 image"
    if not content:
        return None, 8, "content 不能为空"

    # 2. webhook 调用
    try:
        base_url = get_workflow_url()
        # 查找 script_id 和 type
        from backend.db.models import AIVideoScript, AIVideoMaterialItem, AIVideoMaterial, AIVideoScriptImage
        script_id = None
        type_val = "sketch"
        if asset_type == "audio":
            # asset_id -> AIVideoMaterialItem -> material_id -> AIVideoMaterial -> script_id
            item = None
            material = None
            script = None
            try:
                _, SessionLocal = get_engine_and_sessionmaker()
                session = SessionLocal()
                item = session.query(AIVideoMaterialItem).filter_by(id=asset_id).first()
                if item:
                    material = session.query(AIVideoMaterial).filter_by(id=item.material_id).first()
                    if material:
                        script = session.query(AIVideoScript).filter_by(id=material.script_id).first()
                        if script:
                            script_id = script.id
                            type_val = script.type if script.type else "sketch"
                session.close()
            except Exception:
                pass
        else:
            # asset_id -> AIVideoScriptImage -> script_id
            img = None
            script = None
            try:
                _, SessionLocal = get_engine_and_sessionmaker()
                session = SessionLocal()
                img = session.query(AIVideoScriptImage).filter_by(id=asset_id).first()
                if img:
                    script = session.query(AIVideoScript).filter_by(id=img.script_id).first()
                    if script:
                        script_id = script.id
                        type_val = script.type if script.type else "sketch"
                session.close()
            except Exception:
                pass
        if not type_val or type_val not in type_to_external_api:
            return None, 8, f"未配置该视频风格的外部接口: {type_val}"
        if asset_type == "audio":
            if "audio" not in type_to_external_api[type_val]:
                return None, 8, f"未配置该视频风格的 audio 外部接口: {type_val}"
            workflow_url = type_to_external_api[type_val]["audio"].format(base_url=base_url)
        else:
            if "image" not in type_to_external_api[type_val]:
                return None, 8, f"未配置该视频风格的 image 外部接口: {type_val}"
            workflow_url = type_to_external_api[type_val]["image"].format(base_url=base_url)
        payload = {"input": content}
        resp = requests.post(workflow_url, json=payload, timeout=300)
        resp.raise_for_status()
        try:
            result = resp.json()
        except Exception:
            result = {"raw": resp.text}

        resource_url = None
        # 统一处理 result，支持 list/dict，兼容多种 url 字段
        item = None
        if isinstance(result, list) and len(result) > 0:
            item = result[0]
        elif isinstance(result, dict):
            item = result
        if isinstance(item, dict):
            # 优先 imageUrl/audioUrl，其次 url/audio_url
            resource_url = (
                item.get("imageUrl")
                or item.get("audioUrl")
                or item.get("link")
                or item.get("audio_url")
            )
        if not resource_url or not isinstance(resource_url, str):
            return None, 5, f"webhook 返回无效: {result}"
    except Exception as e:
        return None, 5, f"webhook 调用失败: {str(e)}"

    # 3. minio 上传
    try:
        minio_urls = batch_download_and_upload_to_minio([resource_url])
        if not minio_urls or not minio_urls[0]:
            return None, 6, "资源存储失败，未获取到可访问 url"
        minio_url = minio_urls[0]
    except Exception as e:
        return None, 6, f"资源存储失败: {str(e)}"

    # 4. 数据库更新
    _, SessionLocal = get_engine_and_sessionmaker()
    session = SessionLocal()
    try:
        if asset_type == "audio":
            obj = session.query(AIVideoMaterialItem).filter_by(id=asset_id).first()
            if not obj:
                return None, 4, "未找到指定音频素材"
            obj.audio_url = minio_url
            # content 作为新 text
            if content != obj.text:
                obj.text = content
        else:
            obj = session.query(AIVideoScriptImage).filter_by(id=asset_id).first()
            if not obj:
                return None, 4, "未找到指定图片素材"
            obj.url = minio_url
            # content 作为新 prompt，采用 index 匹配原则
            script_id = obj.script_id
            # 获取该 script_id 下所有图片，按 id 升序
            images = session.query(AIVideoScriptImage).filter_by(script_id=script_id).order_by(AIVideoScriptImage.id.asc()).all()
            # 获取该 script_id 下所有 prompt，按 id 升序
            prompts = session.query(AIVideoScriptImagePrompt).filter_by(script_id=script_id).order_by(AIVideoScriptImagePrompt.id.asc()).all()
            # 找到当前图片在 images 中的 index
            img_index = None
            for idx, img in enumerate(images):
                if img.id == obj.id:
                    img_index = idx
                    break
            # 若 index 合法且有对应 prompt，则替换
            if img_index is not None and img_index < len(prompts):
                prompt_obj = prompts[img_index]
                if content != prompt_obj.prompt:
                    prompt_obj.prompt = content
            # 若未找到对应 prompt，则不做替换，且不报错
        session.commit()
        return minio_url, 0, "success"
    except Exception as e:
        session.rollback()
        return None, 7, f"数据库更新失败: {str(e)}"
    finally:
        session.close()