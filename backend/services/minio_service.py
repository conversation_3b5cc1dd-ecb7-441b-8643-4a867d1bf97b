def upload_audio_to_minio(bucket: str = None, object_name: str = None, file_path: str = None, data: bytes = None) -> str:
    """
    上传音频文件到 MinIO（S3），返回可访问的 HTTP URL。

    依赖: pip install minio

    参数:
        bucket (str): 目标桶名，若为 None 则用配置默认 bucket。
        object_name (str): 上传后的对象名（含路径）。
        file_path (str): 本地音频文件路径（二选一）。
        data (bytes): 音频二进制流（二选一）。

    返回:
        str: 可通过 HTTP 访问的音频文件 URL。

    异常:
        抛出异常或返回 None 表示失败。
    """
    from backend.config import get_minio_config
    try:
        from minio import Minio
        from minio.error import S3Error
    except ImportError:
        raise ImportError("请先安装 minio: pip install minio")

    config = get_minio_config()
    endpoint = config.get("endpoint")
    access_key = config.get("access_key")
    secret_key = config.get("secret_key")
    default_bucket = config.get("bucket")
    region = config.get("region", None)

    if not endpoint or not access_key or not secret_key:
        raise ValueError("MinIO 配置不完整，请检查 endpoint/access_key/secret_key")

    if not bucket:
        bucket = default_bucket
    if not bucket:
        raise ValueError("未指定 bucket，且配置中无默认 bucket")
    if not object_name:
        raise ValueError("object_name 不能为空")
    if not (file_path or data):
        raise ValueError("file_path 和 data 至少需提供一个")

    # 处理 endpoint，去除 http(s):// 前缀用于 Minio 客户端
    endpoint_no_proto = endpoint.replace("https://", "").replace("http://", "")

    # 初始化 Minio 客户端
    minio_client = Minio(
        endpoint_no_proto,
        access_key=access_key,
        secret_key=secret_key,
        region=region,
        secure=endpoint.startswith("https://")
    )

    # 检查桶是否存在，不存在则创建
    found = minio_client.bucket_exists(bucket)
    if not found:
        minio_client.make_bucket(bucket)

    # 上传
    try:
        if file_path:
            result = minio_client.fput_object(
                bucket_name=bucket,
                object_name=object_name,
                file_path=file_path,
                content_type="audio/wav"
            )
        else:
            from io import BytesIO
            data_stream = BytesIO(data)
            result = minio_client.put_object(
                bucket_name=bucket,
                object_name=object_name,
                data=data_stream,
                length=len(data),
                content_type="audio/wav"
            )
    except S3Error as e:
        raise RuntimeError(f"MinIO 上传失败: {str(e)}")

    # 生成可访问 URL
    # 直接拼接: http(s)://endpoint/bucket/object_name
    proto = "https" if endpoint.startswith("https://") else "http"
    endpoint_clean = endpoint.replace("https://", "").replace("http://", "").rstrip("/")
    url = f"{proto}://{endpoint_clean}/{bucket}/{object_name}"
    return url
import requests
import uuid
import mimetypes

def batch_download_and_upload_to_minio(
    url_list,
    bucket: str = None
) -> list:
    """
    批量下载外部图片/音频 URL 文件并上传至 MinIO，返回新 MinIO 地址列表。
    支持图片、音频类型自动识别，文件命名唯一，异常处理健全。

    参数:
        url_list (List[str]): 外部图片/音频 URL 列表
        bucket (str): 目标桶名，若为 None 则用配置默认 bucket

    返回:
        List[str]: 新 MinIO 地址列表，失败项为 None
    """
    results = []
    try:
        import filetype
        has_filetype = True
    except ImportError:
        has_filetype = False

    for url in url_list:
        try:
            resp = requests.get(url, timeout=10)
            resp.raise_for_status()
            data = resp.content

            # 类型识别
            ext = None
            mime = None
            if has_filetype:
                kind = filetype.guess(data)
                if kind:
                    ext = kind.extension
                    mime = kind.mime
            if not ext or not mime:
                mime = resp.headers.get("Content-Type")
                ext = mimetypes.guess_extension(mime) if mime else None
                if ext and ext.startswith("."):
                    ext = ext[1:]
                if not ext:
                    ext = "bin"
                if not mime:
                    mime = "application/octet-stream"

            # 仅允许图片/音频
            if not (mime.startswith("image/") or mime.startswith("audio/")):
                results.append(None)
                continue

            # 唯一 object_name
            object_name = f"external/{uuid.uuid4().hex}.{ext}"

            # 上传
            url_in_minio = upload_audio_to_minio(
                bucket=bucket,
                object_name=object_name,
                data=data
            )
            results.append(url_in_minio)
        except Exception as e:
            results.append(None)
    return results