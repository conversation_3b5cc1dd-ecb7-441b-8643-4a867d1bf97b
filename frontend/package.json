{"name": "aipodcast-frontend", "version": "0.1.0", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.11", "@types/react": "^18.0.27", "@types/react-dom": "^18.0.10", "axios": "^1.3.2", "bootstrap": "^5.2.3", "cross-env": "^7.0.3", "oidc-client-ts": "^3.3.0", "react": "^18.2.0", "react-bootstrap": "^2.7.0", "react-dom": "^18.2.0", "react-router-dom": "^6.23.0", "react-scripts": "5.0.1", "typescript": "^4.9.5", "web-vitals": "^2.1.4"}, "scripts": {"start": "cross-env PORT=3008 react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}