/* 自定义CSS样式 */
.centered-title {
  text-align: center !important;
}

.audio-player {
  width: 100%;
  margin-top: 20px;
}

.example-section {
  background-color: #f8f9fa;
  padding: 15px;
  border-radius: 5px;
  margin-bottom: 20px;
}

.language-toggle {
  position: absolute;
  top: 10px;
  right: 10px;
}

/* 流式生成指示器样式 */
.streaming-indicator {
  display: flex;
  align-items: center;
  color: #0d6efd;
  font-weight: 500;
}

.streaming-dot {
  width: 8px;
  height: 8px;
  background-color: #0d6efd;
  border-radius: 50%;
  margin-right: 4px;
  animation: pulse 1.5s infinite ease-in-out;
}

.streaming-dot:nth-child(2) {
  animation-delay: 0.5s;
}

.streaming-dot:nth-child(3) {
  animation-delay: 1s;
}

@keyframes pulse {
  0% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  50% {
    transform: scale(1.2);
    opacity: 1;
  }
  100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
}

/* 添加响应式样式 */
@media (max-width: 768px) {
  .form-container {
    flex-direction: column;
  }
  
  .col-form-label {
    padding-bottom: 0;
  }
  
  .form-control {
    margin-bottom: 15px;
  }
}
.role-voice-row {
  display: flex;
  width: 100%;
  gap: 32px;
}
.role-voice-col {
  flex: 1 1 0;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  min-width: 0;
  max-width: 100%;
  box-sizing: border-box;
}
.role-desc {
  font-weight: 500;
  margin-bottom: 8px;
}
.audio-placeholder-center {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 48px;
  width: 100%;
}
@media (max-width: 768px) {
  .role-voice-row {
    flex-direction: column;
    gap: 16px;
  }
  .role-voice-col {
    width: 100%;
    max-width: 100%;
  }
}
/* 美化播客生成器标题 */
.aipodcast-title-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(120deg, #e0e7ff 0%, #f0faff 100%);
  border-radius: 1.5rem;
  box-shadow: 0 6px 32px 0 rgba(0, 123, 255, 0.10), 0 1.5px 8px 0 rgba(0,0,0,0.04);
  padding: 1.2rem 1rem 0.7rem 1rem;
  margin-bottom: 1.2rem;
  position: relative;
  overflow: hidden;
}

.aipodcast-title-strong {
  font-size: 2rem;
  font-weight: 900;
  letter-spacing: 3px;
  background: linear-gradient(90deg, #007bff 10%, #00c6ff 90%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
  text-shadow: 0 4px 24px rgba(0,123,255,0.10), 0 1.5px 8px 0 rgba(0,0,0,0.04);
  margin-bottom: 0.2rem;
  text-align: center;
  line-height: 1.1;
  transition: font-size 0.2s;
}

.aipodcast-title-sub {
  font-size: 0.95rem;
  color: #5c6b7a;
  opacity: 0.85;
  letter-spacing: 1.5px;
  text-align: center;
  margin-top: 0.1rem;
  font-weight: 400;
  line-height: 1.2;
  text-shadow: 0 1px 4px rgba(0,0,0,0.04);
}

@media (max-width: 600px) {
  .aipodcast-title-strong {
    font-size: 1.4rem;
  }
  .aipodcast-title-wrapper {
    padding: 0.7rem 0.3rem 0.4rem 0.3rem;
  }
}
/* 统一卡片风格 */
.aipodcast-card {
  background: rgba(255,255,255,0.85);
  border-radius: 1.2rem;
  box-shadow: 0 4px 24px 0 rgba(0, 123, 255, 0.08), 0 1.5px 8px 0 rgba(0,0,0,0.04);
  padding: 2rem 1.5rem 1.5rem 1.5rem;
  margin-bottom: 2.5rem;
  border: none;
  position: relative;
  transition: box-shadow 0.2s;
}

.aipodcast-section-title {
  font-size: 1.45rem;
  font-weight: 700;
  letter-spacing: 1.5px;
  color: #007bff;
  margin-bottom: 1.2rem;
  text-shadow: 0 2px 8px rgba(0,123,255,0.08);
  text-align: left;
  border-left: 4px solid #00c6ff;
  padding-left: 0.7rem;
  background: linear-gradient(90deg, #e0f2ff 0%, #f8fbff 100%);
  border-radius: 0.5rem;
  display: inline-block;
}

.aipodcast-table th,
.aipodcast-table td {
  vertical-align: middle !important;
  font-size: 1rem;
  padding: 0.5rem 0.7rem;
}

.aipodcast-table th {
  background: #f0f7ff;
  color: #007bff;
  font-weight: 700;
  border-bottom: 2px solid #e3e3e3;
}

.aipodcast-table tr {
  background: rgba(255,255,255,0.95);
}

.aipodcast-table {
  border-radius: 0.7rem;
  overflow: hidden;
  box-shadow: 0 2px 8px 0 rgba(0,123,255,0.04);
}

.aipodcast-btn-primary {
  background: linear-gradient(90deg, #007bff 0%, #00c6ff 100%);
  border: none;
  color: #fff !important;
  font-weight: 700;
  box-shadow: 0 2px 8px 0 rgba(0,123,255,0.08);
  transition: background 0.2s;
}

.aipodcast-btn-primary:hover, .aipodcast-btn-primary:focus {
  background: linear-gradient(90deg, #0056b3 0%, #00aaff 100%);
  color: #fff !important;
}

.aipodcast-form-label {
  font-weight: 600;
  color: #007bff;
  margin-bottom: 0.3rem;
  letter-spacing: 1px;
}
/* 强制历史记录详情弹层宽度 */
.aipodcast-popover-wide.popover {
  max-width: 900px !important;
  width: 600px !important;
  min-width: 400px;
}
/* ====== 主布局样式微调 ====== */
.aipodcast-root-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  height: 100vh;
  width: 100vw;
  overflow: hidden;
}
.aipodcast-mainrow {
  flex: 1 1 0;
  display: flex;
  min-height: 0;
  width: 100%;
}
.aipodcast-topbar {
  box-shadow: 0 2px 8px 0 rgba(0,0,0,0.04);
  border-bottom: 1px solid #e3e3e3;
  min-height: 56px;
}

.aipodcast-sidebar {
  min-width: 200px;
  max-width: 240px;
  height: 100%;
  box-shadow: 2px 0 12px 0 rgba(0,123,255,0.04);
  border-right: 1.5px solid #e3e3e3;
  background: #f8fafc;
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  padding-left: 0;
  padding-right: 0;
}

.aipodcast-sidebar-item {
  font-size: 1.08rem;
  font-weight: 500;
  color: #3a4a5a !important;
  border-radius: 0.7rem 1.2rem 1.2rem 0;
  margin-right: 8px;
  padding: 0.7rem 1.2rem 0.7rem 1.5rem;
  background: none;
  border: none;
  text-align: left;
  transition: background 0.18s, color 0.18s;
}
.aipodcast-sidebar-item:hover,
.aipodcast-sidebar-item:focus {
  background: linear-gradient(90deg, #e0f2ff 0%, #f0faff 100%);
  color: #007bff !important;
}
.aipodcast-sidebar-item.active {
  background: linear-gradient(90deg, #d0e7ff 0%, #e0f7ff 100%);
  color: #007bff !important;
  font-weight: 700;
  box-shadow: 0 2px 8px 0 rgba(0,123,255,0.06);
}

.aipodcast-maincontent {
  background: #fff;
  flex: 1 1 0;
  min-height: 0;
  height: 100%;
  box-shadow: none;
  padding: 2.5rem 2.5rem 2rem 2.5rem;
  border-left: none;
  border-radius: 0 1.5rem 1.5rem 0;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: stretch;
  overflow: auto;
}

@media (max-width: 900px) {
  .aipodcast-sidebar {
    min-width: 120px;
    max-width: 160px;
    padding-left: 0.2rem;
    padding-right: 0.2rem;
  }
  .aipodcast-maincontent {
    padding: 1.2rem 0.5rem 1rem 0.5rem;
    border-radius: 0;
  }
}
/* ====== 顶部导航栏美化（优化AI播客topbar）====== */
.aipodcast-topbar {
  background: #232b3b;
  box-shadow: none;
  border-bottom: none;
  min-height: 56px;
  border-radius: 0;
  z-index: 100;
  padding-left: 2rem;
  padding-right: 2rem;
  position: relative;
}

.aipodcast-navbar-brand {
  display: flex;
  align-items: center;
  font-size: 1.5rem;
  font-weight: 700;
  letter-spacing: 1px;
  color: #fff;
  background: none;
  margin-left: 0.2rem;
  margin-right: 1.2rem;
  user-select: none;
}
/* .aipodcast-navbar-brand .aipodcast-logo { 移除logo相关样式，极简风格不再需要 } */

@media (max-width: 600px) {
  .aipodcast-topbar {
    min-height: 48px;
    padding-left: 0.7rem;
    padding-right: 0.7rem;
    border-radius: 0;
  }
  .aipodcast-navbar-brand {
    font-size: 1.1rem;
    margin-right: 0.5rem;
  }
}
/* 顶部导航栏浅色风格 */
.aipodcast-topbar {
  background-color: #f8f9fa !important;
  border-bottom: 1px solid #e9ecef;
  box-shadow: none;
}

/* 顶部品牌字体深色简洁 */
.aipodcast-navbar-brand {
  color: #212529 !important;
  font-weight: 600;
  font-size: 1.25rem;
  letter-spacing: 0.02em;
}
/* 顶部导航栏品牌图标极简样式 */
.aipodcast-navbar-icon {
  font-size: 1.3em;
  margin-right: 0.38em;
  color: #6c757d;
  display: inline-flex;
  align-items: center;
  vertical-align: middle;
  user-select: none;
}
/* 移动端 Modal 弹窗适配 */
@media (max-width: 600px) {
  .modal-dialog {
    max-width: 95vw !important;
    width: auto !important;
    margin: 0 auto !important;
  }
  .modal-content {
    max-height: 90vh !important;
    overflow-y: auto !important;
    border-radius: 16px !important;
  }
  .modal-body {
    padding: 1rem !important;
  }
  .modal-header, .modal-footer {
    padding-left: 1rem !important;
    padding-right: 1rem !important;
  }
}