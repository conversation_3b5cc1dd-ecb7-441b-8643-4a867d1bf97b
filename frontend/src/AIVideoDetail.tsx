import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>, Spinner, Image, Button } from 'react-bootstrap';
import { getAIVideoDetail, replaceAsset } from './services/api';
import { AIVideoDetailResponse } from './types';
import { generateAIVideoPart2 } from './services/api';
import { handleDownload } from './utils';

interface DetailProps {
  id: number | string;
  onBack?: () => void;
}

const AIVideoDetail: React.FC<DetailProps> = ({ id, onBack }) => {
  const [data, setData] = useState<AIVideoDetailResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 两步法第二步生成状态
  const [step2Loading, setStep2Loading] = useState(false);
  const [step2Error, setStep2Error] = useState<string | null>(null);
  const [step2Success, setStep2Success] = useState<string | null>(null);

  // 图片素材按钮 loading 状态，key 用图片 url
  const [imgBtnLoading, setImgBtnLoading] = useState<{ [k: string]: boolean }>({});
  // 音频素材按钮 loading 状态，key 用音频 url
  const [audioBtnLoading, setAudioBtnLoading] = useState<{ [k: string]: boolean }>({});
  // 图片替换错误
  const [imgReplaceError, setImgReplaceError] = useState<string | null>(null);
  // 音频替换错误
  const [audioReplaceError, setAudioReplaceError] = useState<string | null>(null);
  // 下载错误
  const [downloadError, setDownloadError] = useState<string | null>(null);

  useEffect(() => {
    setLoading(true);
    setError(null);
    getAIVideoDetail(id)
      .then(res => {
        setData(res.data);
        setLoading(false);
        // 调试：打印后端返回数据
        console.log('AIVideoDetail data:', res);
      })
      .catch(e => {
        setError(e.message || '获取AI视频详情失败');
        setLoading(false);
      });
    // 清理第二步状态
    setStep2Loading(false);
    setStep2Error(null);
    setStep2Success(null);
  }, [id]);

  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ minHeight: 200 }}>
        <Spinner animation="border" />
      </div>
    );
  }

  if (error) {
    return <Alert variant="danger">{error}</Alert>;
  }

  if (!data) {
    return <Alert variant="warning">未找到该视频详情</Alert>;
  }

  return (
    <div className="aipodcast-card" style={{ width: '100%', padding: 10 }}>
      {/* 下载错误提示 */}
      {downloadError && (
        <Alert
          variant="danger"
          onClose={() => setDownloadError(null)}
          dismissible
          className="mb-2"
        >
          {downloadError}
        </Alert>
      )}
      {/* 两步法第二步生成状态提示 */}
      {step2Error && (
        <Alert
          variant="danger"
          onClose={() => setStep2Error(null)}
          dismissible
          className="mb-2"
        >
          {step2Error}
        </Alert>
      )}
      {step2Success && (
        <Alert
          variant="success"
          onClose={() => setStep2Success(null)}
          dismissible
          className="mb-2"
        >
          {step2Success}
        </Alert>
      )}
      <div className="d-flex align-items-center justify-content-between mb-4">
        <div className="aipodcast-section-title mb-0">AI视频详情</div>
        <button
          className="aipodcast-btn-primary"
          style={{
            fontSize: 15,
            borderRadius: '0.7rem',
            padding: '0.35rem 1.2rem',
            minWidth: 96,
            marginLeft: 12,
            boxShadow: '0 2px 8px 0 rgba(0,123,255,0.08)'
          }}
          onClick={() => {
            if (onBack) {
              onBack();
            } else {
              window.location.pathname = '/aivideo';
            }
          }}
        >
          返回列表
        </button>
      </div>
      <div style={{
        background: '#f8fbff',
        borderRadius: '0.8rem',
        boxShadow: '0 1.5px 8px 0 rgba(0,123,255,0.04)',
        padding: '1.5rem 1.2rem 1.2rem 1.2rem',
        marginBottom: '1.5rem',
        border: '1.5px solid #e3e3e3'
      }}>
        <div className="mb-3">
          <div className="aipodcast-form-label mb-1"><b>主题</b></div>
          <div style={{ wordBreak: 'break-all', fontWeight: 500 }}>{data.input}</div>
        </div>
        <div className="mb-3">
          <div className="aipodcast-form-label mb-1"><b>视频名称</b></div>
          <div style={{ wordBreak: 'break-all' }}>{data.name || '未命名视频'}</div>
        </div>
        {typeof data.draft_url === 'string' && data.draft_url && (
          <div className="mb-3">
            <div className="aipodcast-form-label mb-1"><b>草稿链接</b></div>
            <a href={data.draft_url} target="_blank" rel="noopener noreferrer">{data.draft_url}</a>
          </div>
        )}
        {typeof data.debug_url === 'string' && data.debug_url && (
          <div className="mb-3">
            <div className="aipodcast-form-label mb-1"><b>调试链接</b></div>
            <a href={data.debug_url} target="_blank" rel="noopener noreferrer">{data.debug_url}</a>
          </div>
        )}
        {/* 两步法第二步按钮，仅在无draft_url/debug_url时显示 */}
        {(!data.draft_url && !data.debug_url) && (
          <div className="mb-3">
            <div>
              <div className="mb-2" style={{ fontSize: 13, color: '#888' }}>
                两步法：请确认脚本无误后，点击下方按钮生成视频。生成过程可能需要数十秒，请耐心等待。
              </div>
              <Button
                className="aipodcast-btn-primary"
                style={{ minWidth: 180, fontWeight: 500, fontSize: 16, borderRadius: 20 }}
                disabled={step2Loading}
                onClick={async () => {
                  setStep2Loading(true);
                  setStep2Error(null);
                  setStep2Success(null);
                  try {
                    const res = data.content
                      ? await generateAIVideoPart2(data.id, data.content)
                      : await generateAIVideoPart2(data.id);
                    if (res.status === 200) {
                      setStep2Success('第二步生成成功，正在刷新详情...');
                      // 自动刷新详情
                      getAIVideoDetail(data.id)
                        .then(res2 => {
                          setData(res2.data);
                          setStep2Success('第二步生成成功！');
                          setStep2Loading(false);
                        })
                        .catch(e => {
                          setStep2Error('生成成功但刷新详情失败: ' + (e.message || ''));
                          setStep2Loading(false);
                        });
                    } else {
                      setStep2Error('生成失败');
                      setStep2Loading(false);
                    }
                  } catch (error: any) {
                    setStep2Error(error?.message || '生成失败');
                    setStep2Loading(false);
                  }
                }}
                aria-label="生成视频第二步"
              >
                {step2Loading ? (
                  <>
                    <Spinner as="span" animation="border" size="sm" className="me-2" />
                    正在生成视频（第二步）...
                  </>
                ) : (
                  '生成视频（第二步）'
                )}
              </Button>
            </div>
          </div>
        )}
        {/* 视频脚本表格：左侧整体原始文本，右侧分段可编辑 */}
        {(typeof data.content === 'string' && data.content) || (Array.isArray(data.texts) && data.texts.length > 0) ? (() => {
          const contentStr = typeof data.content === 'string' ? data.content : '';
          const textsArr = Array.isArray(data.texts) ? data.texts : [];
          const rowCount = Math.max(1, textsArr.length);
        
          return (
            <div className="mb-3">
              <div className="aipodcast-form-label mb-1"><b>视频脚本</b></div>
              <div style={{ overflowX: 'auto' }}>
                <table className="aipodcast-table" style={{ minWidth: 400, fontSize: 15, borderCollapse: 'collapse' }}>
                  <thead>
                    <tr>
                      <th style={{ width: '40%', background: '#f6f8fa', fontWeight: 500, fontSize: 14, padding: 6, textAlign: 'center' }}>原始文本</th>
                      <th style={{ width: '60%', background: '#f6f8fa', fontWeight: 500, fontSize: 14, padding: 6, textAlign: 'center' }}>可编辑文本</th>
                    </tr>
                  </thead>
                  <tbody>
                    {Array.from({ length: rowCount }).map((_, idx) => (
                      <tr key={idx}>
                        {/* 左侧只在第一行显示，rowSpan 合并 */}
                        {idx === 0 && (
                          <td
                            rowSpan={rowCount}
                            style={{
                              padding: 6,
                              verticalAlign: 'top',
                              background: '#f8f9fa',
                              minWidth: 180,
                              borderRight: '1px solid #e3e3e3'
                            }}
                          >
                            <div
                              style={{
                                minHeight: 60,
                                fontSize: 15,
                                color: '#333',
                                wordBreak: 'break-all',
                                padding: '4px 8px',
                                borderRadius: 6,
                                border: '1px solid #e3e3e3',
                                background: '#f8f9fa',
                                whiteSpace: 'pre-wrap',
                                lineHeight: 1.7
                              }}
                            >
                              {contentStr}
                            </div>
                          </td>
                        )}
                        <td style={{ padding: 4, verticalAlign: 'top' }}>
                          <textarea
                            className="form-control"
                            style={{
                              minHeight: 32,
                              background: '#fff',
                              padding: '4px 8px',
                              borderRadius: 6,
                              border: '1px solid #e3e3e3',
                              fontSize: 15,
                              resize: 'vertical',
                              width: '100%',
                              boxSizing: 'border-box',
                              lineHeight: 1.5
                            }}
                            value={textsArr[idx] || ''}
                            onChange={e => {
                              const newTexts = Array.isArray(data.texts) ? [...data.texts] : [];
                              // 补齐长度
                              while (newTexts.length <= idx) newTexts.push('');
                              newTexts[idx] = e.target.value;
                              setData(prev => prev ? { ...prev, texts: newTexts } : prev);
                            }}
                          />
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
                <div className="d-block d-md-none mt-2" style={{ fontSize: 13, color: '#888' }}>
                  * 可左右滑动查看全部内容
                </div>
              </div>
            </div>
          );
        })() : null}
        {/* 图片素材表格 */}
        {(() => {
          const textsArr = Array.isArray(data.texts) ? data.texts : [];
          const promptsArr = Array.isArray(data.imagePromptList) && data.imagePromptList.length > 0
            ? data.imagePromptList
            : Array.isArray(data.image_prompts) && data.image_prompts.length > 0
              ? data.image_prompts
              : [];
          // 新结构：images 为 ImageItem[]
          const imagesArr = Array.isArray(data.images) && data.images.length > 0
            ? data.images
            : [];
          // 以 imagesArr 为主，补齐 text/prompt
          const rows = imagesArr.map((img, idx) => ({
            id: img.id,
            url: img.url,
            text: textsArr[idx] || '',
            prompt: promptsArr[idx] || '',
          }));
          if (imagesArr.length === 0) return null;
          return (
            <div className="mb-3">
              {/* 图片替换错误提示 */}
              {imgReplaceError && (
                <Alert
                  variant="danger"
                  onClose={() => setImgReplaceError(null)}
                  dismissible
                  className="mb-2"
                >
                  {imgReplaceError}
                </Alert>
              )}
              <div className="aipodcast-form-label mb-2"><b>图片素材</b></div>
              <div style={{ overflowX: 'auto' }}>
                <table className="aipodcast-table" style={{ width: '100%', minWidth: 600, fontSize: 15 }}>
                  <thead>
                    <tr>
                      <th style={{ width: '20%' }}>文本</th>
                      <th style={{ width: '35%' }}>图片提示词</th>
                      <th style={{ width: '30%' }}>图片</th>
                      <th style={{ width: '15%' }}>操作</th>
                    </tr>
                  </thead>
                  <tbody>
                    {rows.map((row, idx) => (
                      <tr key={row.id}>
                        <td style={{ wordBreak: 'break-all', maxWidth: 120 }}>{row.text}</td>
                        <td style={{ wordBreak: 'break-all', maxWidth: 160 }}>
                          <textarea
                            className="form-control"
                            value={row.prompt}
                            style={{
                              width: '100%',
                              minHeight: 100,
                              minWidth: 60,
                              fontSize: 15,
                              borderRadius: 8,
                              border: '1.2px solid #e3e3e3',
                              background: '#f8f9fa',
                              padding: '0.3rem 0.6rem'
                            }}
                            onChange={e => {
                              const newPrompt = e.target.value;
                              setData(prev => {
                                if (!prev) return prev;
                                // 优先 imagePromptList，否则 image_prompts
                                let promptsArr: string[] = [];
                                let key: 'imagePromptList' | 'image_prompts' = 'imagePromptList';
                                if (Array.isArray(prev.imagePromptList) && prev.imagePromptList.length > 0) {
                                  promptsArr = [...prev.imagePromptList];
                                  key = 'imagePromptList';
                                } else if (Array.isArray(prev.image_prompts) && prev.image_prompts.length > 0) {
                                  promptsArr = [...prev.image_prompts];
                                  key = 'image_prompts';
                                } else {
                                  promptsArr = [];
                                  key = 'imagePromptList';
                                }
                                // 补齐长度
                                while (promptsArr.length <= idx) promptsArr.push('');
                                promptsArr[idx] = newPrompt;
                                return { ...prev, [key]: promptsArr };
                              });
                            }}
                          />
                        </td>
                        <td style={{ textAlign: 'center' }}>
                          {row.url
                            ? <Image src={row.url} alt={`img${row.id}`} thumbnail style={{ maxWidth: 120, maxHeight: 120, borderRadius: '0.5rem' }} />
                            : ''}
                        </td>
                        <td style={{ textAlign: 'center' }}>
                          <div style={{ display: 'flex', gap: 8, justifyContent: 'center', flexWrap: 'wrap' }}>
                            <Button
                              size="sm"
                              variant="outline-primary"
                              disabled={!!imgBtnLoading[row.id]}
                              onClick={async () => {
                                if (imgBtnLoading[row.id]) return;
                                setImgBtnLoading(l => ({ ...l, [row.id]: true }));
                                try {
                                  const res = await replaceAsset(
                                    row.id, // 以图片 id 作为 asset_id
                                    "image",
                                    row.prompt || ""
                                  );
                                  setImgBtnLoading(l => ({ ...l, [row.id]: false }));
                                  // 局部刷新逻辑：仅更新 data.images 中对应 id 的 url
                                  if (res?.data?.url) {
                                    setData(prev => {
                                      if (!prev) return prev;
                                      const imagesArr = Array.isArray(prev.images) ? prev.images : [];
                                      const newImages = imagesArr.map(img =>
                                        img.id === row.id
                                          ? { ...img, url: res.data?.url ?? img.url }
                                          : img
                                      );
                                      return { ...prev, images: newImages };
                                    });
                                  } else if (res?.code === 0) {
                                    window.alert("替换成功但未返回新图片地址");
                                  }
                                } catch (e: any) {
                                  setImgBtnLoading(l => ({ ...l, [row.id]: false }));
                                  setImgReplaceError(e?.message || "替换素材失败");
                                }
                              }}
                              style={{ minWidth: 80, fontSize: 15, borderRadius: 16 }}
                              aria-label="替换图片素材"
                            >
                              {imgBtnLoading[row.id] ? (
                                <span>
                                  <Spinner
                                    as="span"
                                    animation="border"
                                    size="sm"
                                    role="status"
                                    aria-hidden="true"
                                    style={{ marginRight: 4 }}
                                  />
                                  替换中
                                </span>
                              ) : (
                                "替换素材"
                              )}
                            </Button>
                            <Button
                              size="sm"
                              variant="outline-secondary"
                              onClick={e => {
                                e.stopPropagation();
                                // 自动根据 url 提取图片后缀，确保下载文件名与资源类型一致
                                let ext = '.jpg';
                                try {
                                  const urlObj = new URL(row.url, window.location.href);
                                  const pathname = urlObj.pathname;
                                  const match = pathname.match(/\.(jpg|jpeg|png|webp|gif|bmp|svg)$/i);
                                  if (match) {
                                    ext = match[0];
                                  }
                                } catch (e) {}
                                handleDownload(row.url);
                              }}
                              disabled={!row.url}
                              style={{ minWidth: 70, fontSize: 15, borderRadius: 16 }}
                              aria-label="下载图片素材"
                            >
                              下载
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
                <div className="d-block d-md-none mt-2" style={{ fontSize: 13, color: '#888' }}>
                  * 可左右滑动查看全部内容
                </div>
              </div>
            </div>
          );
        })()}
        {/* 音频素材表格 */}
        {(Array.isArray(data.materialList) && data.materialList.length > 0
          ? data.materialList
          : Array.isArray(data.materials) && data.materials.length > 0
            ? data.materials
            : null) && (
          <div className="mb-3">
            {/* 音频替换错误提示 */}
            {audioReplaceError && (
              <Alert
                variant="danger"
                onClose={() => setAudioReplaceError(null)}
                dismissible
                className="mb-2"
              >
                {audioReplaceError}
              </Alert>
            )}
            <div className="aipodcast-form-label mb-2"><b>音频素材</b></div>
            <div style={{ overflowX: 'auto' }}>
              <table className="aipodcast-table" style={{ width: '100%', minWidth: 600, fontSize: 15 }}>
                <thead>
                  <tr>
                    <th style={{ width: '50%' }}>文本</th>
                    <th style={{ width: '35%' }}>音频</th>
                    <th style={{ width: '15%' }}>操作</th>
                  </tr>
                </thead>
                <tbody>
                  {(Array.isArray(data.materialList) && data.materialList.length > 0
                    ? data.materialList
                    : Array.isArray(data.materials) && data.materials.length > 0
                      ? data.materials
                      : []
                  ).map((mat: any, idx: number) => {
                    // 新结构：MaterialItem 带 id
                    if (mat && typeof mat === 'object' && typeof mat.id === 'number') {
                      return (
                        <tr key={mat.id}>
                          <td style={{ wordBreak: 'break-all', maxWidth: 180 }}>
                            <input
                              type="text"
                              value={mat.text || ''}
                              style={{
                                width: '100%',
                                minWidth: 80,
                                fontSize: 15,
                                borderRadius: 8,
                                border: '1.2px solid #e3e3e3',
                                background: '#f8f9fa',
                                padding: '0.3rem 0.6rem'
                              }}
                              onChange={e => {
                                const newText = e.target.value;
                                setData(prev => {
                                  if (!prev) return prev;
                                  // 更新 materialList
                                  let newMaterialList = prev.materialList;
                                  if (Array.isArray(prev.materialList)) {
                                    newMaterialList = prev.materialList.map(item =>
                                      item.id === mat.id ? { ...item, text: newText } : item
                                    );
                                  }
                                  // 更新 materials
                                  let newMaterials = prev.materials;
                                  if (Array.isArray(prev.materials)) {
                                    newMaterials = prev.materials.map(item =>
                                      item.id === mat.id ? { ...item, text: newText } : item
                                    );
                                  }
                                  return { ...prev, materialList: newMaterialList, materials: newMaterials };
                                });
                              }}
                            />
                          </td>
                          <td>
                            {mat.audio_url
                              ? <audio controls src={mat.audio_url} style={{ width: 180, borderRadius: '0.5rem', background: '#f8f9fa' }} />
                              : ''}
                          </td>
                          <td style={{ textAlign: 'center' }}>
                            <div style={{ display: 'flex', gap: 8, justifyContent: 'center', flexWrap: 'wrap' }}>
                              <Button
                                size="sm"
                                variant="outline-primary"
                                disabled={!!audioBtnLoading[mat.id]}
                                onClick={async () => {
                                  if (audioBtnLoading[mat.id]) return;
                                  setAudioBtnLoading(l => ({ ...l, [mat.id]: true }));
                                  try {
                                    const res = await replaceAsset(
                                      mat.id, // 以音频 id 作为 asset_id
                                      "audio",
                                      mat.text || ""
                                    );
                                    setAudioBtnLoading(l => ({ ...l, [mat.id]: false }));
                                    // 局部刷新逻辑：仅更新 data.materialList 中对应 id 的 audio_url
                                    if (res && res.data && (typeof (res.data as any).audio_url === 'string' || typeof res.data.url === 'string')) {
                                      // 兼容后端返回 { url } 或 { audio_url }
                                      const newAudioUrl =
                                        typeof (res.data as any).audio_url === 'string'
                                          ? (res.data as any).audio_url
                                          : res.data.url;
                                      setData(prev => {
                                        if (!prev) return prev;
                                        // 兼容 materialList/materials 可能为 undefined/null
                                        const updateList = (list: any[] | undefined | null) =>
                                          Array.isArray(list)
                                            ? list.map(item =>
                                                item.id === mat.id
                                                  ? { ...item, audio_url: newAudioUrl }
                                                  : item
                                              )
                                            : [];
                                        let newData: typeof prev = { ...prev };
                                        if (Array.isArray(prev.materialList)) {
                                          newData.materialList = updateList(prev.materialList);
                                        }
                                        if (Array.isArray(prev.materials)) {
                                          newData.materials = updateList(prev.materials);
                                        }
                                        return newData;
                                      });
                                    } else if (res?.code === 0) {
                                      window.alert("替换成功但未返回新音频地址。");
                                    }
                                  } catch (e: any) {
                                    setAudioBtnLoading(l => ({ ...l, [mat.id]: false }));
                                    setAudioReplaceError(e?.message || "替换素材失败");
                                  }
                                }}
                                style={{ minWidth: 80, fontSize: 15, borderRadius: 16 }}
                                aria-label="替换音频素材"
                              >
                                {audioBtnLoading[mat.id] ? (
                                  <span>
                                    <Spinner
                                      as="span"
                                      animation="border"
                                      size="sm"
                                      role="status"
                                      aria-hidden="true"
                                      style={{ marginRight: 4 }}
                                    />
                                    替换中
                                  </span>
                                ) : (
                                  "替换素材"
                                )}
                              </Button>
                              <Button
                                size="sm"
                                variant="outline-secondary"
                                onClick={() => handleDownload(mat.audio_url)}
                                disabled={!mat.audio_url}
                                style={{ minWidth: 70, fontSize: 15, borderRadius: 16 }}
                                aria-label="下载音频素材"
                              >
                                下载
                              </Button>
                            </div>
                          </td>
                        </tr>
                      );
                    } else if (typeof mat === 'string') {
                      return (
                        <tr key={idx}>
                          <td>{mat}</td>
                          <td></td>
                          <td></td>
                        </tr>
                      );
                    } else {
                      return (
                        <tr key={idx}>
                          <td>{JSON.stringify(mat)}</td>
                          <td></td>
                          <td></td>
                        </tr>
                      );
                    }
                  })}
                </tbody>
              </table>
              <div className="d-block d-md-none mt-2" style={{ fontSize: 13, color: '#888' }}>
                * 可左右滑动查看全部内容
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AIVideoDetail;