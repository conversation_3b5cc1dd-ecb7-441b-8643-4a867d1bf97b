import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Navbar, Nav, Dropdown } from 'react-bootstrap';
import PodcastGenerator from './PodcastGenerator';
import History from './History';
import TTSGenerator from './TTSGenerator';
import AIVideoList from './AIVideoList';
import AIVideoDetail from './AIVideoDetail';
import AuthCallback from './components/AuthCallback';
import SilentCallback from './components/SilentCallback';
import ProtectedRoute from './components/ProtectedRoute';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import './App.css';

// 用户信息组件
const UserInfo: React.FC = () => {
  const { user, logout } = useAuth();

  if (!user) return null;

  return (
    <Dropdown align="end">
      <Dropdown.Toggle variant="outline-secondary" id="user-dropdown" size="sm">
        {user.profile?.name || user.profile?.email || '用户'}
      </Dropdown.Toggle>
      <Dropdown.Menu>
        <Dropdown.Item onClick={logout}>
          登出
        </Dropdown.Item>
      </Dropdown.Menu>
    </Dropdown>
  );
};

// 主应用内容组件
const AppContent: React.FC = () => {
  // 0: 播客生成器, 1: 历史记录, 2: TTS生成, 3: AI视频, 4: AI视频详情
  const [selected, setSelected] = useState<number>(0);
  const [aivideoId, setAIVideoId] = useState<number | null>(null);

  // 菜单项与路由映射
  // 菜单顺序调整：0: AI视频, 1: 播客生成, 2: TTS生成, 3: 历史记录
  const menuRouteMap = [
    { key: 0, path: '/aivideo', label: 'AI 视频' },
    { key: 1, path: '/podcast', label: '播客' },
    { key: 2, path: '/tts', label: 'TTS' },
    { key: 3, path: '/history', label: '历史记录' },
  ];

  // 初始化时根据 URL 或 localStorage 设置 selected
  useEffect(() => {
    const handleRoute = () => {
      const path = window.location.pathname;
      if (path.startsWith('/aivideo/')) {
        // 详情页
        const idStr = path.replace('/aivideo/', '');
        const id = Number(idStr);
        if (!isNaN(id)) {
          setAIVideoId(id);
          setSelected(4);
        } else {
          setAIVideoId(null);
          setSelected(0); // AI视频列表
        }
      } else if (path === '/aivideo') {
        setAIVideoId(null);
        setSelected(0); // AI视频列表
      } else {
        const found = menuRouteMap.find(item => item.path === path);
        if (found) {
          setSelected(found.key);
          localStorage.setItem('selectedMenu', String(found.key));
        } else {
          const stored = localStorage.getItem('selectedMenu');
          const parsed = stored !== null ? Number(stored) : NaN;
          if (typeof parsed === 'number' && !isNaN(parsed)) {
            setSelected(parsed);
          } else {
            setSelected(0);
          }
        }
        setAIVideoId(null);
      }
    };
    handleRoute();
    window.addEventListener('popstate', handleRoute);
    return () => window.removeEventListener('popstate', handleRoute);
  }, []);

  // 切换菜单时，setSelected 并写入 localStorage，并同步 URL
  const handleMenuSelect = (val: number) => {
    setSelected(val);
    localStorage.setItem('selectedMenu', String(val));
    const route = menuRouteMap.find(item => item.key === val);
    if (route) {
      window.history.pushState({}, '', route.path);
      if (val === 0) {
        // 切到AI视频列表时，清空详情
        setAIVideoId(null);
      }
    }
  };

  // AI视频列表点击详情
  const handleAIVideoDetail = (id: number) => {
    setAIVideoId(id);
    setSelected(4);
    window.history.pushState({}, '', `/aivideo/${id}`);
  };

  // AI视频详情返回列表
  const handleAIVideoBack = () => {
    setAIVideoId(null);
    setSelected(0);
    window.history.pushState({}, '', '/aivideo');
  };

  return (
    <Container fluid className="aipodcast-root-container p-0">
      {/* 顶部导航栏 */}
      <Navbar bg="light" variant="light" expand="lg" className="aipodcast-topbar">
        <Navbar.Brand className="aipodcast-navbar-brand">
          <span className="aipodcast-navbar-icon" role="img" aria-label="AI工作台">💡</span>
          { process.env.REACT_APP_TITLE || 'AI 工作台' }
        </Navbar.Brand>
        <Nav className="ms-auto">
          <UserInfo />
        </Nav>
      </Navbar>
      {/* 主体区域 */}
      <Row className="aipodcast-mainrow g-0">
        {/* 侧边栏 */}
        <Col xs={2} md={2} className="aipodcast-sidebar bg-light border-end" style={{ minWidth: 200, maxWidth: 240 }}>
          <Nav className="flex-column py-4">
            <Nav.Link
              as="button"
              className={`aipodcast-sidebar-item mb-2${selected === 0 ? ' active' : ''}`}
              onClick={() => handleMenuSelect(0)}
            >
              AI视频
            </Nav.Link>
            <Nav.Link
              as="button"
              className={`aipodcast-sidebar-item mb-2${selected === 1 ? ' active' : ''}`}
              onClick={() => handleMenuSelect(1)}
            >
              播客生成
            </Nav.Link>
            <Nav.Link
              as="button"
              className={`aipodcast-sidebar-item${selected === 2 ? ' active' : ''}`}
              onClick={() => handleMenuSelect(2)}
            >
              TTS生成
            </Nav.Link>
            <Nav.Link
              as="button"
              className={`aipodcast-sidebar-item mb-2${selected === 3 ? ' active' : ''}`}
              onClick={() => handleMenuSelect(3)}
            >
              历史记录
            </Nav.Link>
          </Nav>
        </Col>
        {/* 主内容区 */}
        <Col xs={10} md={10} className="aipodcast-maincontent">
          {selected === 0 ? (
            <AIVideoList onSelectDetail={handleAIVideoDetail} />
          ) : selected === 1 ? (
            <PodcastGenerator />
          ) : selected === 2 ? (
            <TTSGenerator />
          ) : selected === 3 ? (
            <History />
          ) : selected === 4 && aivideoId ? (
            <AIVideoDetail id={aivideoId} onBack={handleAIVideoBack} />
          ) : (
            <div style={{ fontSize: 20, color: '#888' }}>请选择菜单</div>
          )}
        </Col>
      </Row>
    </Container>
  );
};

// 路由处理组件
const AppRouter: React.FC = () => {
  const path = window.location.pathname;

  // 处理认证回调
  if (path === '/auth/callback') {
    return <AuthCallback />;
  }

  // 处理静默回调
  if (path === '/auth/silent-callback') {
    return <SilentCallback />;
  }

  // 主应用内容
  return (
    <ProtectedRoute>
      <AppContent />
    </ProtectedRoute>
  );
};

// 主App组件
const App: React.FC = () => {
  return (
    <AuthProvider>
      <AppRouter />
    </AuthProvider>
  );
};

export default App;