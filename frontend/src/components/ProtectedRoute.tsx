import React from 'react';
import { Container, Spinner } from 'react-bootstrap';
import { useAuth } from '../contexts/AuthContext';
import Login from './Login';

interface ProtectedRouteProps {
  children: React.ReactNode;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const { isAuthenticated, isLoading } = useAuth();

  // 显示加载状态
  if (isLoading) {
    return (
      <Container className="d-flex justify-content-center align-items-center min-vh-100">
        <div className="text-center">
          <Spinner animation="border" role="status" className="mb-3">
            <span className="visually-hidden">Loading...</span>
          </Spinner>
          <p>正在验证身份...</p>
        </div>
      </Container>
    );
  }

  // 如果未认证，显示登录页面
  if (!isAuthenticated) {
    return <Login />;
  }

  // 已认证，显示受保护的内容
  return <>{children}</>;
};

export default ProtectedRoute;
