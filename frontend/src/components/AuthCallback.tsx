import React, { useEffect, useState } from 'react';
import { Container, Spinner, Alert } from 'react-bootstrap';
import authService from '../services/auth';

const AuthCallback: React.FC = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const handleCallback = async () => {
      try {
        setIsLoading(true);
        await authService.handleCallback();
        
        // 登录成功，重定向到主页
        window.location.href = '/';
      } catch (err) {
        console.error('Callback handling failed:', err);
        setError('登录回调处理失败，请重试');
      } finally {
        setIsLoading(false);
      }
    };

    handleCallback();
  }, []);

  if (isLoading) {
    return (
      <Container className="d-flex justify-content-center align-items-center min-vh-100">
        <div className="text-center">
          <Spinner animation="border" role="status" className="mb-3">
            <span className="visually-hidden">Loading...</span>
          </Spinner>
          <p>正在处理登录...</p>
        </div>
      </Container>
    );
  }

  if (error) {
    return (
      <Container className="d-flex justify-content-center align-items-center min-vh-100">
        <Alert variant="danger" className="text-center">
          <Alert.Heading>登录失败</Alert.Heading>
          <p>{error}</p>
          <hr />
          <div className="d-flex justify-content-end">
            <button
              className="btn btn-outline-danger"
              onClick={() => window.location.href = '/'}
            >
              返回首页
            </button>
          </div>
        </Alert>
      </Container>
    );
  }

  return null;
};

export default AuthCallback;
