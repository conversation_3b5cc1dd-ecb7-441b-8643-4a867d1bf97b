import React, { useEffect } from 'react';
import authService from '../services/auth';

const SilentCallback: React.FC = () => {
  useEffect(() => {
    const handleSilentCallback = async () => {
      try {
        await authService.handleSilentCallback();
      } catch (error) {
        console.error('Silent callback failed:', error);
      }
    };

    handleSilentCallback();
  }, []);

  return (
    <div style={{ display: 'none' }}>
      Silent callback processing...
    </div>
  );
};

export default SilentCallback;
