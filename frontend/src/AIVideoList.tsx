import React, { useEffect, useState } from 'react';
import { <PERSON>, Pa<PERSON>ation, <PERSON><PERSON>, Spin<PERSON>, <PERSON>dal, Button, Form } from 'react-bootstrap';
import { getAIVideoList, generateAIVideo, generateAIVideoPart1 } from './services/api';
import { AIVideoBasic } from './types';

interface ListState {
  data: AIVideoBasic[];
  total: number;
  page: number;
  limit: number;
  loading: boolean;
  error: string | null;
}

/**
 * 格式化日期为“YYYY-MM-DD”或“YYYY年MM月DD日”格式，兼容字符串和Date对象
 */
function formatDate(date: string | Date | undefined): string {
  if (!date) return '';
  let d: Date;
  if (typeof date === 'string') {
    // 兼容ISO字符串
    d = new Date(date);
    if (isNaN(d.getTime())) return date; // 解析失败，原样返回
  } else if (date instanceof Date) {
    d = date;
  } else {
    return '';
  }
  // 输出格式：YYYY-MM-DD HH:mm
  const y = d.getFullYear();
  const m = (d.getMonth() + 1).toString().padStart(2, '0');
  const day = d.getDate().toString().padStart(2, '0');
  const hh = d.getHours().toString().padStart(2, '0');
  const mm = d.getMinutes().toString().padStart(2, '0');
  return `${y}-${m}-${day} ${hh}:${mm}`;
}

const AIVideoList: React.FC<{ onSelectDetail?: (id: number) => void }> = ({ onSelectDetail }) => {
  const [state, setState] = useState<ListState>({
    data: [],
    total: 0,
    page: 1,
    limit: 10,
    loading: false,
    error: null,
  });

  // 新增：生成相关状态
  const [showModal, setShowModal] = useState(false);
  // mode 状态本地存储：惰性初始化
  const [mode, setMode] = useState<'one' | 'two'>(() => {
    try {
      const val = localStorage.getItem('AIVideoList_Mode');
      if (val === 'one' || val === 'two') {
        return val;
      }
    } catch (e) {
      // ignore
    }
    return 'one';
  }); // 一步法/两步法
  const [form, setForm] = useState({ input: ''});
  const [genLoading, setGenLoading] = useState(false);
  const [genError, setGenError] = useState<string | null>(null);
  const [genSuccess, setGenSuccess] = useState<string | null>(null);

  const fetchList = async (page: number, limit: number) => {
    setState(s => ({ ...s, loading: true, error: null }));
    try {
      const res = await getAIVideoList(page, limit);
      setState(s => ({
        ...s,
        data: res.data,
        total: res.total,
        page: res.page,
        limit: res.limit,
        loading: false,
        error: null,
      }));
    } catch (e: any) {
      setState(s => ({ ...s, loading: false, error: e.message || '获取AI视频列表失败' }));
    }
  };

  useEffect(() => {
    fetchList(state.page, state.limit);
    // eslint-disable-next-line
  }, []);

  // 兜底：组件挂载后如 localStorage 有非法值，强制回退为 'one'
  useEffect(() => {
    try {
      const val = localStorage.getItem('AIVideoList_Mode');
      if (val !== null && val !== 'one' && val !== 'two') {
        localStorage.setItem('AIVideoList_Mode', 'one');
        setMode('one');
      }
    } catch (e) {
      // ignore
    }
  }, []);

  const handlePageChange = (page: number) => {
    fetchList(page, state.limit);
  };

  const handleRowClick = (id: number) => {
    if (onSelectDetail) {
      onSelectDetail(id);
    } else {
      window.history.pushState({}, '', `/aivideo/${id}`);
      // 触发父组件监听
      window.dispatchEvent(new PopStateEvent('popstate'));
    }
  };

  // 生成入口相关
  const handleOpenModal = () => {
    setForm({ input: ''});
    setGenError(null);
    setGenSuccess(null);
    setShowModal(true);
  };
  const handleCloseModal = () => {
    setShowModal(false);
    setGenError(null);
    setGenSuccess(null);
  };

  const handleFormChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  const handleModeChange = (m: 'one' | 'two') => {
    setMode(m);
    try {
      localStorage.setItem('AIVideoList_Mode', m);
    } catch (e) {
      // ignore
    }
    setGenError(null);
    setGenSuccess(null);
  };

  const handleGen = async () => {
    if (!form.input.trim()) {
      setGenError('主题不能为空');
      return;
    }
    setGenLoading(true);
    setGenError(null);
    setGenSuccess(null);
    try {
      if (mode === 'one') {
        const res = await generateAIVideo(form.input);
        if (res.status === 200) {
          setGenSuccess('生成成功！');
          fetchList(1, state.limit);
          setTimeout(() => {
            setShowModal(false);
          }, 1000);
        } else {
          setGenError('生成失败');
        }
      } else {
        const res = await generateAIVideoPart1(form.input);
        if (res.status === 200) {
          setGenSuccess('脚本生成成功！请在详情页完成第二步。');
          fetchList(1, state.limit);
          setTimeout(() => {
            setShowModal(false);
          }, 1000);
        } else {
          setGenError('生成失败');
        }
      }
    } catch (e: any) {
      setGenError(e.message || '生成失败');
    } finally {
      setGenLoading(false);
    }
  };

  // 计算分页
  const totalPages = Math.ceil(state.total / state.limit);

  return (
    <div className="aipodcast-card" style={{ width: '100%', padding: 10 }}>
      {/* 顶部生成入口与切换 */}
      <div className="d-flex align-items-center mb-3 flex-wrap" style={{ gap: 16 }}>
        <div>
          <Button
            variant={mode === 'one' ? 'primary' : 'outline-primary'}
            size="sm"
            style={{ borderRadius: 16, marginRight: 8, fontWeight: 600, minWidth: 80 }}
            onClick={() => handleModeChange('one')}
            disabled={genLoading}
            aria-label="切换到一步法"
          >
            一步法
          </Button>
          <Button
            variant={mode === 'two' ? 'primary' : 'outline-primary'}
            size="sm"
            style={{ borderRadius: 16, fontWeight: 600, minWidth: 80 }}
            onClick={() => handleModeChange('two')}
            disabled={genLoading}
            aria-label="切换到两步法"
          >
            两步法
          </Button>
        </div>
        <Button
          variant="success"
          size="sm"
          style={{ borderRadius: 16, fontWeight: 600, minWidth: 120 }}
          onClick={handleOpenModal}
          disabled={genLoading}
          aria-label={mode === 'one' ? '生成AI视频' : '生成脚本（第一步）'}
        >
          {genLoading ? (
            <>
              <Spinner animation="border" size="sm" className="me-2" />
              {mode === 'one' ? '生成中...' : '生成中...'}
            </>
          ) : (
            mode === 'one' ? '生成AI视频' : '生成脚本（第一步）'
          )}
        </Button>
      </div>
      <div className="mb-2" style={{ fontSize: 14, color: '#888' }}>
        当前模式：<b>{mode === 'one' ? '一步法' : '两步法'}</b>。
        {mode === 'one'
          ? ' 一步法：输入主题和名称，直接生成AI视频。'
          : ' 两步法：先生成脚本，后在详情页生成视频，适合需要编辑脚本的场景。'}
      </div>

      {/* 生成弹窗 */}
      <Modal show={showModal} onHide={handleCloseModal} centered>
        <Modal.Header closeButton>
          <Modal.Title>
            {mode === 'one' ? '一步法生成AI视频' : '两步法第一步：生成脚本'}
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <div className="mb-2" style={{ fontSize: 13, color: '#888' }}>
            {mode === 'one'
              ? '请输入主题，点击生成后将直接创建AI视频。'
              : '请输入主题，点击生成后将生成脚本，需在详情页完成第二步生成视频。'}
          </div>
          <Form autoComplete="off">
            <Form.Group className="mb-3" controlId="formInput">
              <Form.Label>主题</Form.Label>
              <Form.Control
                type="text"
                name="input"
                value={form.input}
                onChange={handleFormChange}
                placeholder="请输入视频主题"
                disabled={genLoading}
                autoFocus
                maxLength={40}
                aria-required="true"
                aria-label="视频主题"
                style={{ fontSize: 15 }}
              />
            </Form.Group>
            {genError && (
              <Alert variant="danger" className="py-2" style={{ fontSize: 14 }}>
                {genError}
              </Alert>
            )}
            {genSuccess && (
              <Alert variant="success" className="py-2" style={{ fontSize: 14 }}>
                {genSuccess}
              </Alert>
            )}
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={handleCloseModal} disabled={genLoading}>
            取消
          </Button>
          <Button
            variant="primary"
            onClick={handleGen}
            disabled={genLoading}
            style={{ minWidth: 90 }}
            aria-label={mode === 'one' ? '生成' : '生成脚本'}
          >
            {genLoading ? (
              <>
                <Spinner animation="border" size="sm" className="me-2" />
                {mode === 'one' ? '生成中...' : '生成中...'}
              </>
            ) : (
              mode === 'one' ? '生成' : '生成脚本'
            )}
          </Button>
        </Modal.Footer>
      </Modal>

      <div className="aipodcast-section-title mb-3">AI视频列表</div>
      {state.error && <Alert variant="danger" className="mb-3">{state.error}</Alert>}
      {state.loading ? (
        <div className="d-flex justify-content-center align-items-center" style={{ minHeight: 200 }}>
          <Spinner animation="border" />
        </div>
      ) : (
        <>
          <Table className="aipodcast-table mb-3" size="sm" responsive>
            <thead>
              <tr>
                <th style={{ width: 60 }}>ID</th>
                <th style={{ minWidth: 120 }}>主题</th>
                <th style={{ minWidth: 120 }}>名称</th>
                <th style={{ minWidth: 140 }}>创建时间</th>
              </tr>
            </thead>
            <tbody>
              {state.data.length === 0 ? (
                <tr>
                  <td colSpan={4} className="text-center text-muted">暂无数据</td>
                </tr>
              ) : (
                state.data.map(item => (
                  <tr
                    key={item.id}
                    style={{ cursor: 'pointer', transition: 'background 0.15s' }}
                    onClick={() => handleRowClick(item.id)}
                    className="table-row-hover"
                  >
                    <td>{item.id}</td>
                    <td style={{ maxWidth: 200, whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' }}>{item.input}</td>
                    <td>{item.name}</td>
                    <td>{formatDate(item.created_at)}</td>
                  </tr>
                ))
              )}
            </tbody>
          </Table>
          {totalPages > 1 && (
            <Pagination className="justify-content-center mt-4" style={{ gap: 6 }}>
              <Pagination.First
                onClick={() => handlePageChange(1)}
                disabled={state.page === 1}
                className="aipodcast-btn-primary"
                style={{ borderRadius: '0.7rem', minWidth: 36 }}
              />
              <Pagination.Prev
                onClick={() => handlePageChange(state.page - 1)}
                disabled={state.page === 1}
                className="aipodcast-btn-primary"
                style={{ borderRadius: '0.7rem', minWidth: 36 }}
              />
              {Array.from({ length: totalPages }, (_, i) => (
                <Pagination.Item
                  key={i + 1}
                  active={state.page === i + 1}
                  onClick={() => handlePageChange(i + 1)}
                  className={state.page === i + 1 ? "aipodcast-btn-primary" : ""}
                  style={{ borderRadius: '0.7rem', minWidth: 36, fontWeight: 600 }}
                >
                  {i + 1}
                </Pagination.Item>
              ))}
              <Pagination.Next
                onClick={() => handlePageChange(state.page + 1)}
                disabled={state.page === totalPages}
                className="aipodcast-btn-primary"
                style={{ borderRadius: '0.7rem', minWidth: 36 }}
              />
              <Pagination.Last
                onClick={() => handlePageChange(totalPages)}
                disabled={state.page === totalPages}
                className="aipodcast-btn-primary"
                style={{ borderRadius: '0.7rem', minWidth: 36 }}
              />
            </Pagination>
          )}
        </>
      )}
    </div>
  );
};

export default AIVideoList;