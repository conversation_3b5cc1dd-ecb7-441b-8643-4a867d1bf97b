import axios from 'axios';
import { DialogueItem, ApiResponse, VoiceRoleItem, AIVideoBasic, AIVideoDetailResponse, AIVideoListResponse } from '../types';

// API基础URL
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5008/api';

// 创建axios实例
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

/**
 * 生成音频
 * @param jsonDialogue 对话JSON字符串
 * @param language 语言('en'或'zh')
 * @param voiceMap 角色-voice对象数组（可选），每项含 key/desc
 * @returns 包含音频数据的响应
 */
export const generateAudio = async (
  jsonDialogue: string,
  language: string,
  voiceMap?: VoiceRoleItem[]
): Promise<ApiResponse> => {
  try {
    const payload: any = {
      json_dialogue: jsonDialogue,
      language: language,
    };
    if (voiceMap) {
      payload.voice_map = JSON.stringify(voiceMap);
    }
    const response = await apiClient.post<ApiResponse>('/podcast/generate/audio', payload, {
      headers: {
        'Content-Type': 'application/json'
      }
    });
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data.error || 'Failed to generate audio');
    }
    throw new Error('Network error occurred');
  }
};

/**
 * 生成播客脚本
 * @param inputText 输入的文章文本
 * @param language 语言('en'或'zh')
 * @returns 包含脚本数据的响应
 */
export const generateScript = async (inputText: string, language: string): Promise<{script: DialogueItem[]}> => {
  try {
    const response = await apiClient.post('/podcast/generate/script', {
      input_text: inputText,
      language: language
    }, {
      headers: {
        'Content-Type': 'application/json'
      }
    });
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data.error || 'Failed to generate script');
    }
    throw new Error('Network error occurred');
  }
};

/**
 * 获取所有可用角色
 * @param lang 可选，指定语言（如 'zh' 或 'en'），只返回该语言数据
 * @returns 角色对象，结构为 { zh: [{ role, voices }], en?: [{ role, voices }] }
 */
export const getVoiceRoles = async (
  lang?: string
): Promise<{
  zh: { role: string; voices: VoiceRoleItem[] }[];
  en?: { role: string; voices: VoiceRoleItem[] }[];
}> => {
  try {
    const url = `${API_BASE_URL}/podcast/voice_roles${lang ? `?lang=${encodeURIComponent(lang)}` : ''}`;
    const response = await axios.get(url);
    const data = response.data || {};

    // 处理API实际返回的格式: { zh: [{ role: "0", voices: [{ key, desc }] }], en?: [...] }
    if (data.zh && Array.isArray(data.zh)) {
      const processedZh = data.zh.map((roleData: any) => ({
        role: roleData.role || '0',
        voices: (roleData.voices || []).map((voice: any) => ({
          key: voice.key || '',
          desc: voice.desc || ''
        }))
      }));

      const result: {
        zh: { role: string; voices: VoiceRoleItem[] }[];
        en?: { role: string; voices: VoiceRoleItem[] }[];
      } = { zh: processedZh };

      // 如果有英文数据也处理
      if (data.en && Array.isArray(data.en)) {
        result.en = data.en.map((roleData: any) => ({
          role: roleData.role || '0',
          voices: (roleData.voices || []).map((voice: any) => ({
            key: voice.key || '',
            desc: voice.desc || ''
          }))
        }));
      }

      return result;
    }

    // 如果没有预期的数据结构，返回空结构
    return { zh: [] };
  } catch (error) {
    throw new Error('无法获取角色列表');
  }
};
// 获取脚本历史记录
export const getScriptHistory = async (): Promise<any[]> => {
  try {
    const response = await apiClient.get('/podcast/history/scripts');
    return response.data;
  } catch (error) {
    throw new Error('无法获取脚本历史记录');
  }
};

// 获取音频历史记录
export const getAudioHistory = async (): Promise<any[]> => {
  try {
    const response = await apiClient.get('/podcast/history/audios');
    return response.data;
  } catch (error) {
    throw new Error('无法获取音频历史记录');
  }
};
/**
 * 音频后期合成
 * @param id 音频ID
 * @returns 接口响应
 */
export const editAudio = async (id: number): Promise<any> => {
  try {
    const payload = { id };
    const response = await apiClient.post(
      '/podcast/edit/audio',
      payload,
      {
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data.error || '音频后期合成失败');
    }
    throw new Error('网络错误');
  }
};
// 获取TTS语音生成历史（分页）
export const getTTSHistory = async (page: number = 1, limit: number = 10): Promise<{
  data: {
    id: number;
    text: string;
    voice: string;
    created_at: string;
    audio_url: string;
  }[];
  total: number;
  page: number;
  limit: number;
}> => {
  try {
    const response = await apiClient.get('/tts/history', {
      params: { page, limit }
    });
    return response.data;
  } catch (error) {
    throw new Error('无法获取TTS语音生成历史');
  }
};

/**
 * 获取TTS可用voice列表，返回 VoiceRoleItem[]，每项含 key/desc。
 */
export const getTTSVoices = async (): Promise<VoiceRoleItem[]> => {
  try {
    const response = await apiClient.get('/tts/voices');
    // 假设返回 { voices: [{key, desc}] }
    return response.data.voices || [];
  } catch (error) {
    throw new Error('无法获取TTS语音列表');
  }
};

// 生成TTS音频（voice 参数为 voice key）
export const generateTTS = async (text: string, voiceKey: string): Promise<{ audio_url: string }> => {
  try {
    const response = await apiClient.post('/tts/generate', { text, voice: voiceKey });
    return response.data;
  } catch (error) {
    throw new Error('TTS音频生成失败');
  }
};
// AI 视频相关类型
/**
 * 获取AI视频列表（分页）
 * @param page 页码
 * @param limit 每页数量
 */
export const getAIVideoList = async (
  page: number = 1,
  limit: number = 10
): Promise<AIVideoListResponse> => {
  try {
    const response = await apiClient.get('/aivideo/list', {
      params: { page, limit }
    });
    return response.data;
  } catch (error) {
    throw new Error('无法获取AI视频列表');
  }
};

/**
 * 获取AI视频详情
 * @param id 视频ID
 */
export const getAIVideoDetail = async (
  id: number | string
): Promise<{ data: AIVideoDetailResponse }> => {
  try {
    const response = await apiClient.get('/aivideo/detail', {
      params: { id }
    });
    return response.data;
  } catch (error) {
    throw new Error('无法获取AI视频详情');
  }
};

/**
 * 替换AI视频素材
 * @param asset_id 素材ID
 * @param type 素材类型
 * @param content 新内容
 * @returns Promise<{ code: number, msg: string, data?: { url: string } }>
 */
export const replaceAsset = async (
  asset_id: number | string,
  type: string,
  content: string
): Promise<{ code: number; msg: string; data?: { url: string } }> => {
  try {
    const payload = { asset_id, type, content };
    const response = await apiClient.post('/aivideo/replace_asset', payload, {
      headers: { 'Content-Type': 'application/json' }
    });
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data.error || '素材替换失败');
    }
    throw new Error('网络错误');
  }
};

/**
 * 一步法生成AI视频
 * @param input 输入主题
 * @returns Promise<{ code: number, msg: string }>
 */
export const generateAIVideo = async (
  input: string
): Promise<{ data: any[]; status: number }> => {
  try {
    const payload = { input };
    const response = await apiClient.post('/aivideo/generate', payload, {
      headers: { 'Content-Type': 'application/json' }
    });
    const { data, status } = response;
    if (status === 200) {
      return { data, status };
    } else {
      throw new Error(`AI视频生成失败: status=${status}`);
    }
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data.error || 'AI视频生成失败');
    }
    throw new Error('网络错误');
  }
};

/**
 * 两步法第一步：生成AI视频脚本
 * @param input 输入主题
 * @returns Promise<{ code: number, msg: string }>
 */
export const generateAIVideoPart1 = async (
  input: string
): Promise<{ data: any[]; status: number }> => {
  try {
    const payload = { input };
    const response = await apiClient.post('/aivideo/generate_part1', payload, {
      headers: { 'Content-Type': 'application/json' }
    });
    const { data, status } = response;
    if (status === 200) {
      return { data, status };
    } else {
      throw new Error(`AI视频脚本生成失败: status=${status}`);
    }
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data.error || 'AI视频脚本生成失败');
    }
    throw new Error('网络错误');
  }
};

/**
 * 两步法第二步：生成AI视频（根据脚本）
 * @param id 视频ID
 * @returns Promise<{ code: number, msg: string }>
 */
export const generateAIVideoPart2 = async (
  id: number | string,
  content?: string
): Promise<{ data: any[]; status: number }> => {
  try {
    const payload: { id: number | string; content?: string } = { id };
    if (content !== undefined) {
      payload.content = content;
    }
    const response = await apiClient.post('/aivideo/generate_part2', payload, {
      headers: { 'Content-Type': 'application/json' }
    });
    const { data, status } = response;
    if (status === 200) {
      return { data, status };
    } else {
      throw new Error(`AI视频第二步生成失败: status=${status}`);
    }
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data.error || 'AI视频第二步生成失败');
    }
    throw new Error('网络错误');
  }
};